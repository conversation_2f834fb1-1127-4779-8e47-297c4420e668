import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi } from 'vitest';
import RoleUpdateModal from '../RoleUpdateModal';
import { UpdateUserRolesDto, RoleUpdateValidationDto } from '../../../apis/userManagement';

// Mock the ConfirmationModal component
vi.mock('../../ui/ConfirmationModal', () => ({
  default: ({ isOpen, onConfirm, onClose, title, message }: any) => 
    isOpen ? (
      <div data-testid="confirmation-modal">
        <h3>{title}</h3>
        <p>{message}</p>
        <button onClick={onConfirm}>Confirm</button>
        <button onClick={onClose}>Cancel</button>
      </div>
    ) : null
}));

// Mock the Avatar component
vi.mock('../../Common/Avatar', () => ({
  default: ({ name }: { name: string }) => <div data-testid="avatar">{name}</div>
}));

describe('RoleUpdateModal', () => {
  const mockUser = {
    id: 'user-123',
    name: 'Test User',
    email: '<EMAIL>',
    role: 'user' as const,
    status: 'active' as const,
    joinDate: '2024-01-01',
    reportsCount: 5,
    lastActive: '2024-01-15',
    phone: '+1234567890',
    location: 'Test City'
  };

  const mockProps = {
    user: mockUser,
    isOpen: true,
    onClose: vi.fn(),
    onSave: vi.fn(),
    onValidate: vi.fn(),
    availableRoles: ['admin', 'cj', 'user'],
    isLoading: false,
    isValidating: false
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders the modal when open', () => {
    render(<RoleUpdateModal {...mockProps} />);
    
    expect(screen.getByText('Update User Roles')).toBeInTheDocument();
    expect(screen.getByText('Editing: Test User')).toBeInTheDocument();
    expect(screen.getByText('User ID: user-123')).toBeInTheDocument();
    expect(screen.getByText('Current Role: Regular User')).toBeInTheDocument();
  });

  it('does not render when closed', () => {
    render(<RoleUpdateModal {...mockProps} isOpen={false} />);
    
    expect(screen.queryByText('Update User Roles')).not.toBeInTheDocument();
  });

  it('initializes with current user role selected', () => {
    render(<RoleUpdateModal {...mockProps} />);
    
    const userCheckbox = screen.getByRole('checkbox', { name: /Regular User/ });
    expect(userCheckbox).toBeChecked();
    
    const adminCheckbox = screen.getByRole('checkbox', { name: /Administrator/ });
    expect(adminCheckbox).not.toBeChecked();
  });

  it('allows selecting multiple roles', async () => {
    const user = userEvent.setup();
    render(<RoleUpdateModal {...mockProps} />);
    
    const adminCheckbox = screen.getByRole('checkbox', { name: /Administrator/ });
    const cjCheckbox = screen.getByRole('checkbox', { name: /CJ Officer/ });
    
    await user.click(adminCheckbox);
    await user.click(cjCheckbox);
    
    expect(adminCheckbox).toBeChecked();
    expect(cjCheckbox).toBeChecked();
  });

  it('requires reason for admin role changes', async () => {
    const user = userEvent.setup();
    render(<RoleUpdateModal {...mockProps} />);
    
    // Add admin role
    const adminCheckbox = screen.getByRole('checkbox', { name: /Administrator/ });
    await user.click(adminCheckbox);
    
    // Try to submit without reason
    const submitButton = screen.getByRole('button', { name: /Validate & Update/ });
    await user.click(submitButton);
    
    expect(screen.getByText('Reason is required for admin role changes')).toBeInTheDocument();
  });

  it('validates roles before submission', async () => {
    const user = userEvent.setup();
    const mockValidation: RoleUpdateValidationDto = {
      canUpdate: true,
      warnings: ['This will grant admin privileges'],
      blockers: [],
      affectedPermissions: ['User Management', 'System Administration'],
      requiresConfirmation: false
    };
    
    mockProps.onValidate.mockResolvedValue(mockValidation);
    
    render(<RoleUpdateModal {...mockProps} />);
    
    // Add admin role and reason
    const adminCheckbox = screen.getByRole('checkbox', { name: /Administrator/ });
    await user.click(adminCheckbox);
    
    const reasonTextarea = screen.getByRole('textbox', { name: /Reason for Change/ });
    await user.type(reasonTextarea, 'Promoting to admin role');
    
    // Submit form
    const submitButton = screen.getByRole('button', { name: /Validate & Update/ });
    await user.click(submitButton);
    
    await waitFor(() => {
      expect(mockProps.onValidate).toHaveBeenCalledWith('user-123', {
        roleNames: expect.arrayContaining(['user', 'admin']),
        reason: 'Promoting to admin role'
      });
    });
    
    // Check validation results are displayed
    expect(screen.getByText('This will grant admin privileges')).toBeInTheDocument();
    expect(screen.getByText('User Management')).toBeInTheDocument();
  });

  it('shows confirmation dialog for critical changes', async () => {
    const user = userEvent.setup();
    const mockValidation: RoleUpdateValidationDto = {
      canUpdate: true,
      warnings: [],
      blockers: [],
      affectedPermissions: [],
      requiresConfirmation: true
    };
    
    mockProps.onValidate.mockResolvedValue(mockValidation);
    
    render(<RoleUpdateModal {...mockProps} />);
    
    // Add admin role and reason
    const adminCheckbox = screen.getByRole('checkbox', { name: /Administrator/ });
    await user.click(adminCheckbox);
    
    const reasonTextarea = screen.getByRole('textbox', { name: /Reason for Change/ });
    await user.type(reasonTextarea, 'Promoting to admin role');
    
    // Submit form
    const submitButton = screen.getByRole('button', { name: /Validate & Update/ });
    await user.click(submitButton);
    
    await waitFor(() => {
      expect(screen.getByTestId('confirmation-modal')).toBeInTheDocument();
    });
  });

  it('calls onSave when form is submitted successfully', async () => {
    const user = userEvent.setup();
    const mockValidation: RoleUpdateValidationDto = {
      canUpdate: true,
      warnings: [],
      blockers: [],
      affectedPermissions: [],
      requiresConfirmation: false
    };
    
    mockProps.onValidate.mockResolvedValue(mockValidation);
    mockProps.onSave.mockResolvedValue(undefined);
    
    render(<RoleUpdateModal {...mockProps} />);
    
    // Add CJ role (no reason required)
    const cjCheckbox = screen.getByRole('checkbox', { name: /CJ Officer/ });
    await user.click(cjCheckbox);
    
    // Submit form
    const submitButton = screen.getByRole('button', { name: /Validate & Update/ });
    await user.click(submitButton);
    
    await waitFor(() => {
      expect(mockProps.onSave).toHaveBeenCalledWith('user-123', {
        roleNames: expect.arrayContaining(['user', 'cj']),
        reason: undefined
      });
    });
  });

  it('prevents submission when no roles are selected', async () => {
    const user = userEvent.setup();
    render(<RoleUpdateModal {...mockProps} />);
    
    // Uncheck the current role
    const userCheckbox = screen.getByRole('checkbox', { name: /Regular User/ });
    await user.click(userCheckbox);
    
    // Try to submit
    const submitButton = screen.getByRole('button', { name: /Validate & Update/ });
    await user.click(submitButton);
    
    expect(screen.getByText('At least one role must be selected')).toBeInTheDocument();
    expect(mockProps.onValidate).not.toHaveBeenCalled();
  });

  it('closes modal when close button is clicked', async () => {
    const user = userEvent.setup();
    render(<RoleUpdateModal {...mockProps} />);
    
    const closeButton = screen.getByRole('button', { name: '' }); // X button
    await user.click(closeButton);
    
    expect(mockProps.onClose).toHaveBeenCalled();
  });

  it('shows loading state during validation', () => {
    render(<RoleUpdateModal {...mockProps} isValidating={true} />);
    
    expect(screen.getByText('Validating...')).toBeInTheDocument();
  });

  it('shows loading state during save', async () => {
    const user = userEvent.setup();

    // Mock onSave to return a promise that doesn't resolve immediately
    let resolveSave: () => void;
    const savePromise = new Promise<void>((resolve) => {
      resolveSave = resolve;
    });
    mockProps.onSave.mockReturnValue(savePromise);

    const mockValidation: RoleUpdateValidationDto = {
      canUpdate: true,
      warnings: [],
      blockers: [],
      affectedPermissions: [],
      requiresConfirmation: false
    };
    mockProps.onValidate.mockResolvedValue(mockValidation);

    render(<RoleUpdateModal {...mockProps} />);

    // Add CJ role
    const cjCheckbox = screen.getByRole('checkbox', { name: /CJ Officer/ });
    await user.click(cjCheckbox);

    // Submit form
    const submitButton = screen.getByRole('button', { name: /Validate & Update/ });
    await user.click(submitButton);

    // Should show updating state
    await waitFor(() => {
      expect(screen.getByText('Updating...')).toBeInTheDocument();
    });

    // Resolve the save promise
    resolveSave!();
  });
});
