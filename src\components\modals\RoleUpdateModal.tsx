import React, { useState, useEffect } from 'react';
import {
  X,
  Shield,
  Save,
  Loader2,
  AlertCircle,
  AlertTriangle,
  CheckCircle,
  Info
} from 'lucide-react';
import Avatar from '../Common/Avatar';
import ConfirmationModal from '../ui/ConfirmationModal';
import { UpdateUserRolesDto, RoleUpdateValidationDto } from '../../apis/userManagement';

interface User {
  id: string;
  name: string;
  email: string;
  phone?: string;
  role: 'admin' | 'cj' | 'user';
  status: 'active' | 'inactive' | 'suspended';
  joinDate: string;
  location?: string;
  reportsCount: number;
  lastActive: string;
  avatar?: string;
}

interface RoleUpdateModalProps {
  user: User | null;
  isOpen: boolean;
  onClose: () => void;
  onSave: (userId: string, roleData: UpdateUserRolesDto) => Promise<void>;
  onValidate: (userId: string, roleData: UpdateUserRolesDto) => Promise<RoleUpdateValidationDto>;
  availableRoles: string[];
  isLoading?: boolean;
  isValidating?: boolean;
}

interface FormData {
  roleNames: string[];
  reason: string;
}

interface FormErrors {
  roleNames?: string;
  reason?: string;
}

const RoleUpdateModal: React.FC<RoleUpdateModalProps> = ({ 
  user, 
  isOpen, 
  onClose, 
  onSave, 
  onValidate,
  availableRoles,
  isLoading = false,
  isValidating = false
}) => {
  const [formData, setFormData] = useState<FormData>({
    roleNames: [],
    reason: ''
  });
  
  const [errors, setErrors] = useState<FormErrors>({});
  const [isSaving, setIsSaving] = useState(false);
  const [validation, setValidation] = useState<RoleUpdateValidationDto | null>(null);
  const [showConfirmation, setShowConfirmation] = useState(false);

  // Initialize form data when user changes
  useEffect(() => {
    if (user) {
      setFormData({
        roleNames: [user.role] || [],
        reason: ''
      });
      setErrors({});
      setValidation(null);
      setShowConfirmation(false);
    }
  }, [user]);

  if (!isOpen || !user) return null;

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    // Role validation
    if (formData.roleNames.length === 0) {
      newErrors.roleNames = 'At least one role must be selected';
    }

    // Reason validation for critical role changes
    const isChangingToAdmin = formData.roleNames.includes('admin') && !user.role.includes('admin');
    const isRemovingAdmin = !formData.roleNames.includes('admin') && user.role.includes('admin');
    
    if ((isChangingToAdmin || isRemovingAdmin) && !formData.reason.trim()) {
      newErrors.reason = 'Reason is required for admin role changes';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleValidateRoles = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      const validationResult = await onValidate(user.id, {
        roleNames: formData.roleNames,
        reason: formData.reason.trim() || undefined
      });
      
      setValidation(validationResult);
      
      if (validationResult.requiresConfirmation) {
        setShowConfirmation(true);
      } else if (validationResult.canUpdate) {
        await handleSubmit();
      }
    } catch (error) {
      console.error('Failed to validate role update:', error);
    }
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    setIsSaving(true);
    try {
      await onSave(user.id, {
        roleNames: formData.roleNames,
        reason: formData.reason.trim() || undefined
      });
      onClose();
    } catch (error) {
      console.error('Failed to update user roles:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleConfirmUpdate = async () => {
    setShowConfirmation(false);
    await handleSubmit();
  };

  const handleRoleChange = (role: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      roleNames: checked 
        ? [...prev.roleNames, role]
        : prev.roleNames.filter(r => r !== role)
    }));
    // Clear validation when roles change
    setValidation(null);
  };

  const getRoleDisplayName = (role: string): string => {
    switch (role.toLowerCase()) {
      case 'admin': return 'Administrator';
      case 'cj': return 'CJ Officer';
      case 'user': return 'Regular User';
      default: return role;
    }
  };

  const hasRoleChanges = () => {
    const currentRoles = [user.role];
    return JSON.stringify(currentRoles.sort()) !== JSON.stringify(formData.roleNames.sort());
  };

  return (
    <>
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div className="bg-white rounded-2xl shadow-2xl max-w-lg w-full max-h-[90vh] overflow-y-auto">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <h2 className="text-2xl font-bold text-gray-900">Update User Roles</h2>
            <button
              onClick={onClose}
              disabled={isSaving}
              className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors disabled:opacity-50"
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          {/* User Info */}
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center space-x-4">
              <Avatar 
                src={user.avatar} 
                name={user.name} 
                size="lg"
              />
              <div>
                <h3 className="text-lg font-semibold text-gray-900">
                  Editing: {user.name}
                </h3>
                <p className="text-sm text-gray-500">
                  User ID: {user.id}
                </p>
                <p className="text-sm text-gray-500">
                  Current Role: {getRoleDisplayName(user.role)}
                </p>
              </div>
            </div>
          </div>

          {/* Form */}
          <form onSubmit={(e) => { e.preventDefault(); handleValidateRoles(); }} className="p-6 space-y-6">
            {/* Role Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                <Shield className="w-4 h-4 inline mr-2" />
                Roles *
              </label>
              <div className="space-y-2">
                {availableRoles.map(role => (
                  <label key={role} className="flex items-center space-x-3">
                    <input
                      type="checkbox"
                      checked={formData.roleNames.includes(role.toLowerCase())}
                      onChange={(e) => handleRoleChange(role.toLowerCase(), e.target.checked)}
                      className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                      disabled={isSaving || isValidating}
                    />
                    <span className="text-sm text-gray-700">
                      {getRoleDisplayName(role)}
                    </span>
                  </label>
                ))}
              </div>
              {errors.roleNames && (
                <p className="mt-1 text-sm text-red-600 flex items-center">
                  <AlertCircle className="w-4 h-4 mr-1" />
                  {errors.roleNames}
                </p>
              )}
            </div>

            {/* Reason */}
            <div>
              <label htmlFor="reason" className="block text-sm font-medium text-gray-700 mb-2">
                Reason for Change
                {(formData.roleNames.includes('admin') || user.role === 'admin') && (
                  <span className="text-red-500 ml-1">*</span>
                )}
              </label>
              <textarea
                id="reason"
                value={formData.reason}
                onChange={(e) => setFormData(prev => ({ ...prev, reason: e.target.value }))}
                placeholder="Explain the reason for this role change..."
                rows={3}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none ${
                  errors.reason ? 'border-red-300' : 'border-gray-300'
                }`}
                disabled={isSaving || isValidating}
              />
              {errors.reason && (
                <p className="mt-1 text-sm text-red-600 flex items-center">
                  <AlertCircle className="w-4 h-4 mr-1" />
                  {errors.reason}
                </p>
              )}
            </div>

            {/* Validation Results */}
            {validation && (
              <div className="space-y-3">
                {validation.warnings.length > 0 && (
                  <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <div className="flex items-start">
                      <AlertTriangle className="w-5 h-5 text-yellow-600 mt-0.5 mr-2 flex-shrink-0" />
                      <div>
                        <h4 className="text-sm font-medium text-yellow-800">Warnings</h4>
                        <ul className="mt-1 text-sm text-yellow-700 list-disc list-inside">
                          {validation.warnings.map((warning, index) => (
                            <li key={index}>{warning}</li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </div>
                )}

                {validation.blockers.length > 0 && (
                  <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                    <div className="flex items-start">
                      <AlertCircle className="w-5 h-5 text-red-600 mt-0.5 mr-2 flex-shrink-0" />
                      <div>
                        <h4 className="text-sm font-medium text-red-800">Cannot Update</h4>
                        <ul className="mt-1 text-sm text-red-700 list-disc list-inside">
                          {validation.blockers.map((blocker, index) => (
                            <li key={index}>{blocker}</li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </div>
                )}

                {validation.affectedPermissions.length > 0 && (
                  <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <div className="flex items-start">
                      <Info className="w-5 h-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
                      <div>
                        <h4 className="text-sm font-medium text-blue-800">Permission Changes</h4>
                        <ul className="mt-1 text-sm text-blue-700 list-disc list-inside">
                          {validation.affectedPermissions.map((permission, index) => (
                            <li key={index}>{permission}</li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Actions */}
            <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
              <button
                type="button"
                onClick={onClose}
                disabled={isSaving || isValidating}
                className="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors disabled:opacity-50"
              >
                Cancel
              </button>
              
              {!validation || validation.canUpdate ? (
                <button
                  type="submit"
                  disabled={isSaving || isValidating || !hasRoleChanges()}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 flex items-center"
                >
                  {isValidating ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Validating...
                    </>
                  ) : isSaving ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Updating...
                    </>
                  ) : (
                    <>
                      <Save className="w-4 h-4 mr-2" />
                      {validation ? 'Update Roles' : 'Validate & Update'}
                    </>
                  )}
                </button>
              ) : null}
            </div>
          </form>
        </div>
      </div>

      {/* Confirmation Modal */}
      <ConfirmationModal
        isOpen={showConfirmation}
        onClose={() => setShowConfirmation(false)}
        onConfirm={handleConfirmUpdate}
        title="Confirm Role Update"
        message={`Are you sure you want to update the roles for ${user.name}? This action will change their permissions immediately.`}
        confirmText="Update Roles"
        cancelText="Cancel"
        type="warning"
      />
    </>
  );
};

export default RoleUpdateModal;
